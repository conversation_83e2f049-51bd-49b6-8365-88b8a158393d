/* eslint-disable @typescript-eslint/no-explicit-any */
import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";
import { restoreCroppedCanvas } from "../operations/crop";

export const createHandleCrop = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        isUndoing,
        containerRef,
        originalImageUrl
      );
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "crop-rect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "crop-rect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    // Get the crop rectangle coordinates - use object properties instead of bounding rect
    // to avoid viewport transform issues
    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = cropRect.getScaledWidth();
    const height = cropRect.getScaledHeight();

    cropRect.set({ visible: false });
    canvas.remove(cropRect);
    canvas.discardActiveObject();

    if (containerRef?.current) {
      const containerBounds = containerRef.current.getBoundingClientRect();
      const aspectRatio = width / height;

      let newWidth = containerBounds.width;
      let newHeight = newWidth / aspectRatio;

      if (newHeight > containerBounds.height) {
        newHeight = containerBounds.height;
        newWidth = newHeight * aspectRatio;
      }

      canvas.setDimensions({ width: newWidth, height: newHeight });

      const scale = Math.min(newWidth / width, newHeight / height);
      const vpt: [number, number, number, number, number, number] = [
        scale,
        0,
        0,
        scale,
        -left * scale,
        -top * scale,
      ];

      canvas.setViewportTransform(vpt);
    }

    const imageWidth = canvas.backgroundImage?.width || 512;
    const imageHeight = canvas.backgroundImage?.height || 512;
    const imageScale = canvas.backgroundImage?.scaleX || 1;

    const scaledImageWidth = imageWidth * imageScale;
    const scaledImageHeight = imageHeight * imageScale;

    const normalizedCropRect = {
      left: left / scaledImageWidth,
      top: top / scaledImageHeight,
      width: width / scaledImageWidth,
      height: height / scaledImageHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };
    setCropData(cropResult);

    canvas.renderAll();
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

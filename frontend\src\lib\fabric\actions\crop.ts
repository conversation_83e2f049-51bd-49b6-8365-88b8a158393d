import { Canvas } from "fabric";
import { CropData } from "@/shared/types";
import { handleCropOperation } from "../operations/crop";

export const createHandleCrop = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return handleCropOperation(
    fabricCanvas,
    hasPerformedCrop,
    setCropData,
    isUndoing,
    setHasPerformedCrop,
    containerRef,
    originalImageUrl
  );
};

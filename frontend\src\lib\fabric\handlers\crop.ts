/* eslint-disable @typescript-eslint/no-explicit-any */
import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";
import { restoreCroppedCanvas } from "../operations/crop";

export const createHandleCrop = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        isUndoing,
        containerRef,
        originalImageUrl
      );
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "crop-rect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "crop-rect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    // Get the crop rectangle's bounding box to ensure we capture the exact drawn area
    const boundingRect = cropRect.getBoundingRect();
    const left = boundingRect.left;
    const top = boundingRect.top;
    const width = boundingRect.width;
    const height = boundingRect.height;

    cropRect.set({ visible: false });
    canvas.remove(cropRect);
    canvas.discardActiveObject();

    if (containerRef?.current) {
      const containerBounds = containerRef.current.getBoundingClientRect();
      const aspectRatio = width / height;

      let newWidth = containerBounds.width;
      let newHeight = newWidth / aspectRatio;

      if (newHeight > containerBounds.height) {
        newHeight = containerBounds.height;
        newWidth = newHeight * aspectRatio;
      }

      canvas.setDimensions({ width: newWidth, height: newHeight });

      const scale = Math.max(newWidth / width, newHeight / height);
      const vpt: [number, number, number, number, number, number] = [
        scale,
        0,
        0,
        scale,
        -left * scale,
        -top * scale,
      ];
      canvas.setViewportTransform(vpt);
    }

    // Get the actual image dimensions accounting for current viewport transform
    const imageWidth = canvas.backgroundImage?.width || 512;
    const imageHeight = canvas.backgroundImage?.height || 512;
    const imageScale = canvas.backgroundImage?.scaleX || 1;

    // Account for viewport transform when calculating normalized coordinates
    const vpt = canvas.viewportTransform;
    const viewportScale = vpt ? vpt[0] : 1;

    // Adjust crop coordinates for viewport transform
    const adjustedLeft = vpt ? (left + vpt[4]) / viewportScale : left;
    const adjustedTop = vpt ? (top + vpt[5]) / viewportScale : top;
    const adjustedWidth = width / viewportScale;
    const adjustedHeight = height / viewportScale;

    const normalizedCropRect = {
      left: adjustedLeft / (imageWidth * imageScale),
      top: adjustedTop / (imageHeight * imageScale),
      width: adjustedWidth / (imageWidth * imageScale),
      height: adjustedHeight / (imageHeight * imageScale),
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };
    setCropData(cropResult);

    canvas.renderAll();
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

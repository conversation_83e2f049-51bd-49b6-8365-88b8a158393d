import { useState } from "react";
import { Canvas } from "fabric";
import { CropData, CropManagementState, UndoTrackingState } from "@/shared/types";
import { handleCropOperation } from "@/lib/fabric/operations/crop";

export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);

  const handleCrop = handleCropOperation(
    fabricCanvas,
    hasPerformedCrop,
    setCropData,
    undoTracking.isUndoingRef,
    setHasPerformedCrop,
    containerRef,
    originalImageUrl
  );

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
  };
};

import { Canvas, Textbox } from "fabric";
import { CropData } from "@/shared/types";
import {
  RESIZE_THRESHOLD,
  MIN_CANVAS_WIDTH,
  MIN_CANVAS_HEIGHT,
  DEFAULT_TEXT_SIZE,
  DEFAULT_TEXT_SCALE,
  BACKGROUND_IMAGE_NAME,
  DEFAULT_STROKE_UNIFORM,
  VERTICAL_ROTATION_ANGLES,
} from "@/config/defaultFabricConfigs";

// Calculate target dimensions that fit within container while maintaining aspect ratio
export const calculateFittedCanvasDimensions = (
  contentWidth: number,
  contentHeight: number,
  containerWidth: number,
  containerHeight: number
) => {
  const aspectRatio = contentWidth / contentHeight;
  let targetWidth = containerWidth;
  let targetHeight = targetWidth / aspectRatio;

  if (targetHeight > containerHeight) {
    targetHeight = containerHeight;
    targetWidth = targetHeight * aspectRatio;
  }

  return { width: targetWidth, height: targetHeight };
};

// Check if resize is needed based on threshold
export const shouldResize = (
  currentWidth: number,
  currentHeight: number,
  targetWidth: number,
  targetHeight: number,
  threshold: number = RESIZE_THRESHOLD
): boolean => {
  return (
    Math.abs(currentWidth - targetWidth) >= threshold ||
    Math.abs(currentHeight - targetHeight) >= threshold
  );
};

// Apply minimum canvas dimensions
export const applyMinimumDimensions = (width: number, height: number) => {
  return {
    width: Math.max(width, MIN_CANVAS_WIDTH),
    height: Math.max(height, MIN_CANVAS_HEIGHT),
  };
};

// Scale and position objects during canvas resize
export const scaleCanvasObjects = (canvas: Canvas, imageScale: number) => {
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== BACKGROUND_IMAGE_NAME) {
      obj.set({
        scaleX: (obj.scaleX || 1) * imageScale,
        scaleY: (obj.scaleY || 1) * imageScale,
        left: (obj.left || 0) * imageScale,
        top: (obj.top || 0) * imageScale,
      });

      if ("strokeUniform" in obj) obj.strokeUniform = DEFAULT_STROKE_UNIFORM;

      if (obj.type === "textbox" || obj.type === "text") {
        const textbox = obj as Textbox;
        textbox.fontSize = DEFAULT_TEXT_SIZE;
        textbox.set({
          scaleX: DEFAULT_TEXT_SCALE,
          scaleY: DEFAULT_TEXT_SCALE,
          lockScalingX: true,
          lockScalingY: true,
          hasControls: false,
        });
      }

      obj.setCoords();
    }
  });
};

// Position background image based on rotation
export const positionBackgroundImage = (
  bgImg: any,
  actualWidth: number,
  actualHeight: number,
  imageScale: number
) => {
  const currentAngle = bgImg.angle || 0;
  const currentFlipX = bgImg.flipX || false;
  const currentFlipY = bgImg.flipY || false;

  bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
  bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;

  if (VERTICAL_ROTATION_ANGLES.includes(currentAngle)) {
    bgImg.left = 0;
    bgImg.top = 0;
  } else {
    bgImg.set({
      left: actualWidth / 2,
      top: actualHeight / 2,
      originX: "center",
      originY: "center",
    });
  }

  bgImg.angle = currentAngle;
  bgImg.flipX = currentFlipX;
  bgImg.flipY = currentFlipY;
};

// Calculate crop viewport transform
export const calculateCropViewportTransform = (
  newCanvasWidth: number,
  newCanvasHeight: number,
  cropLeft: number,
  cropTop: number,
  cropWidth: number,
  cropHeight: number
): [number, number, number, number, number, number] => {
  const cropScale = Math.min(newCanvasWidth / cropWidth, newCanvasHeight / cropHeight);
  const newTranslateX = -cropLeft * cropScale;
  const newTranslateY = -cropTop * cropScale;

  return [cropScale, 0, 0, cropScale, newTranslateX, newTranslateY];
};

// Update crop data with new container dimensions
export const updateCropDataDimensions = (
  cropData: CropData,
  containerWidth: number,
  containerHeight: number
): CropData => {
  return {
    ...cropData,
    canvasDimensions: {
      width: containerWidth,
      height: containerHeight,
    },
  };
};

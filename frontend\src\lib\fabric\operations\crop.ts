/* eslint-disable @typescript-eslint/no-explicit-any */
import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";
import { loadCanvasImage } from "../rendering/image";
import { calculateFittedCanvasDimensions } from "../rendering/resize";
import {
  DEFAULT_IMAGE_WIDTH,
  DEFAULT_IMAGE_HEIGHT,
  DEFAULT_OBJECT_SELECTABLE,
  DEFAULT_OBJECT_EVENTED,
} from "@/config/defaultFabricConfigs";

// Create a DOMRect-like object from crop dimensions
export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

// Calculate scaled image dimensions
export const getScaledImageDimensions = (canvas: Canvas) => {
  const bgImage = canvas.backgroundImage;
  if (!bgImage) {
    return {
      originalWidth: DEFAULT_IMAGE_WIDTH,
      originalHeight: DEFAULT_IMAGE_HEIGHT,
      scale: 1,
      scaledWidth: DEFAULT_IMAGE_WIDTH,
      scaledHeight: DEFAULT_IMAGE_HEIGHT,
    };
  }

  const originalWidth = bgImage.width || DEFAULT_IMAGE_WIDTH;
  const originalHeight = bgImage.height || DEFAULT_IMAGE_HEIGHT;
  const scale = bgImage.scaleX || 1;
  const scaledWidth = originalWidth * scale;
  const scaledHeight = originalHeight * scale;

  return {
    originalWidth,
    originalHeight,
    scale,
    scaledWidth,
    scaledHeight,
  };
};

// Calculate crop rectangle coordinates in canvas space
export const calculateCropCoordinates = (
  cropData: CropData,
  scaledWidth: number,
  scaledHeight: number
) => {
  if (!cropData.normalizedCropRect) {
    throw new Error("Normalized crop rect is required");
  }

  return {
    left: cropData.normalizedCropRect.left * scaledWidth,
    top: cropData.normalizedCropRect.top * scaledHeight,
    width: cropData.normalizedCropRect.width * scaledWidth,
    height: cropData.normalizedCropRect.height * scaledHeight,
  };
};

// Create a clip rectangle for cropping
export const createClipRect = (left: number, top: number, width: number, height: number): Rect => {
  return new Rect({
    left,
    top,
    width,
    height,
    absolutePositioned: true,
    selectable: DEFAULT_OBJECT_SELECTABLE,
    evented: DEFAULT_OBJECT_EVENTED,
  });
};

// Calculate viewport transform for crop display
export const calculateCropViewportTransform = (
  canvasWidth: number,
  canvasHeight: number,
  cropLeft: number,
  cropTop: number,
  cropWidth: number,
  cropHeight: number
): [number, number, number, number, number, number] => {
  const scale = Math.min(canvasWidth / cropWidth, canvasHeight / cropHeight);

  return [scale, 0, 0, scale, -cropLeft * scale, -cropTop * scale];
};

// Apply crop to canvas with all necessary transformations
export const applyCropToCanvas = (
  canvas: Canvas,
  cropData: CropData,
  containerRect?: DOMRect
): void => {
  if (!cropData.normalizedCropRect) return;

  const { scaledWidth, scaledHeight } = getScaledImageDimensions(canvas);
  const { left, top, width, height } = calculateCropCoordinates(
    cropData,
    scaledWidth,
    scaledHeight
  );

  // Create and apply clip path
  const clipRect = createClipRect(left, top, width, height);
  canvas.clipPath = clipRect;

  // Calculate and apply canvas dimensions and viewport transform
  const viewportContainer = cropData.canvasDimensions || containerRect;
  if (viewportContainer) {
    const { width: newWidth, height: newHeight } = calculateFittedCanvasDimensions(
      width,
      height,
      viewportContainer.width,
      viewportContainer.height
    );

    canvas.setDimensions({ width: newWidth, height: newHeight });

    const vpt = calculateCropViewportTransform(newWidth, newHeight, left, top, width, height);
    canvas.setViewportTransform(vpt);
  }

  canvas.renderAll();
};

// Handle crop operation
export const handleCropOperation = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        isUndoing,
        containerRef,
        originalImageUrl
      );
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "crop-rect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "crop-rect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    // Get the crop rectangle coordinates - use the actual rectangle dimensions
    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    cropRect.set({ visible: false });
    canvas.remove(cropRect);
    canvas.discardActiveObject();

    if (containerRef?.current) {
      const containerBounds = containerRef.current.getBoundingClientRect();

      // Calculate how much to scale the crop area to fit the container
      const aspectRatio = width / height;
      let targetWidth = containerBounds.width;
      let targetHeight = targetWidth / aspectRatio;

      if (targetHeight > containerBounds.height) {
        targetHeight = containerBounds.height;
        targetWidth = targetHeight * aspectRatio;
      }

      // Set the canvas to the target size
      canvas.setDimensions({ width: targetWidth, height: targetHeight });

      // Calculate the scale factor to zoom into the crop area
      const scaleX = targetWidth / width;
      const scaleY = targetHeight / height;

      // Create viewport transform that:
      // 1. Scales up to zoom into the crop area
      // 2. Translates to position the crop area at (0,0)
      const vpt: [number, number, number, number, number, number] = [
        scaleX, // Scale X
        0, // Skew Y
        0, // Skew X
        scaleY, // Scale Y
        -left * scaleX, // Translate X (move crop area to left edge)
        -top * scaleY, // Translate Y (move crop area to top edge)
      ];

      console.log("Crop Debug - Simple approach:", {
        cropArea: { left, top, width, height },
        containerSize: { width: containerBounds.width, height: containerBounds.height },
        targetCanvasSize: { targetWidth, targetHeight },
        scale: { scaleX, scaleY },
        translation: { x: -left * scaleX, y: -top * scaleY },
        viewportTransform: vpt,
      });

      // Apply the viewport transform
      canvas.setViewportTransform(vpt);
      canvas.renderAll();
    }

    // Get the actual rendered dimensions of the background image
    const bgImage = canvas.backgroundImage;
    if (!bgImage) return;

    const imageWidth = bgImage.width || DEFAULT_IMAGE_WIDTH;
    const imageHeight = bgImage.height || DEFAULT_IMAGE_HEIGHT;
    const imageScaleX = bgImage.scaleX || 1;
    const imageScaleY = bgImage.scaleY || 1;

    // Calculate the actual rendered size of the background image
    const renderedImageWidth = imageWidth * imageScaleX;
    const renderedImageHeight = imageHeight * imageScaleY;

    // Get background image position (typically at origin for our use case)
    const bgImageLeft = bgImage.left || 0;
    const bgImageTop = bgImage.top || 0;

    // Calculate crop coordinates relative to the background image
    const relativeLeft = left - bgImageLeft;
    const relativeTop = top - bgImageTop;

    console.log("Crop Debug - Background image info:", {
      bgImagePosition: { bgImageLeft, bgImageTop },
      bgImageSize: { imageWidth, imageHeight, imageScaleX, imageScaleY },
      renderedImageSize: { renderedImageWidth, renderedImageHeight },
      cropInImageSpace: { relativeLeft, relativeTop, width, height },
    });

    const normalizedCropRect = {
      left: relativeLeft / renderedImageWidth,
      top: relativeTop / renderedImageHeight,
      width: width / renderedImageWidth,
      height: height / renderedImageHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };
    setCropData(cropResult);

    canvas.renderAll();
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

// Restore canvas from cropped state
export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  undoTrackingRef: React.MutableRefObject<boolean>,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  if (!canvas) return;

  undoTrackingRef.current = true;

  canvas.clipPath = undefined;
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

  if (containerRef?.current) {
    const containerBounds = containerRef.current.getBoundingClientRect();
    canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
  }

  if (originalImageUrl?.current) {
    const containerRect = containerRef?.current?.getBoundingClientRect();
    await loadCanvasImage(canvas, originalImageUrl.current, {
      containerRect: containerRect || undefined,
    });
  }

  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });

  setHasPerformedCrop(false);
  canvas.renderAll();
  undoTrackingRef.current = false;
};

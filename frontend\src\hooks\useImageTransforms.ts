import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState, ImageTransformsState } from "@/shared/types";
import {
  createHandleRotate,
  createHandleFlipHorizontal,
  createHandleFlipVertical,
} from "@/lib/fabric/handlers";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations/transforms";

export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState = {
    rotations: 0,
    flipHorizontal: false,
    flipVertical: false,
  }
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const handleRotate = createHandleRotate(fabricCanvas, setTransformState);
  const handleFlipHorizontal = createHandleFlipHorizontal(fabricCanvas, setTransformState);
  const handleFlipVertical = createHandleFlipVertical(fabricCanvas, setTransformState);

  const applySavedTransforms = () => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    for (let i = 0; i < transformState.rotations; i++) {
      applyCanvasRotation(canvas);
    }

    if (transformState.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas);
    }

    if (transformState.flipVertical) {
      applyCanvasFlipVertical(canvas);
    }
  };

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    applySavedTransforms,
  };
};
